import { defineStore } from 'pinia'
import { BIZ_JnrmAlert_APIS } from '@/api/biz/admin/jnrmalertapi'

export default defineStore('biz-alert', {
	state() {
		return {
			isInit: false,
			list: [],
			isPolling: false,
			pollTimer: null as ReturnType<typeof setInterval> | null,
		}
	},
	actions: {
		async getList() {
			// 如果还未初始化，先初始化数据并启动轮询
			if (!this.isInit) {
				this.isInit = true
				await this.refreshList()
				this.startPolling()
			}
			return this.list
		},

		async refreshList() {
			try {
				const res = await BIZ_JnrmAlert_APIS.getPage({ pageSize: 100 })
				this.list = res.list || []
			} catch (error) {
				console.error('获取告警列表失败:', error)
			}
		},

		startPolling() {
			// 如果已经在轮询，则不重复启动
			if (this.isPolling) {
				return
			}

			this.isPolling = true

			// 设置15秒轮询
			this.pollTimer = setInterval(() => {
				this.refreshList()
			}, 15000) // 15秒 = 15000毫秒
		},

		stopPolling() {
			if (this.pollTimer) {
				clearInterval(this.pollTimer)
				this.pollTimer = null
			}
			this.isPolling = false
		},

		// 处理告警
		async handleAlert(alertId: number, handleMethod: string) {
			try {
				// 调用真实的处理告警API
				await BIZ_JnrmAlert_APIS.handle({
					id: alertId,
					handleMethod: handleMethod,
				})

				// 更新本地状态 - 找到对应的告警并更新其状态
				const alert = this.list.find((item) => item.id === alertId)
				if (alert) {
					alert.handleStatus = true
					alert.handleMethod = handleMethod
					alert.handleTime = new Date().toISOString()
					// 如果需要，也可以设置处理人信息
					// alert.handleUserName = '当前用户' // 这里可以从用户store获取当前用户信息
				}

				return true
			} catch (error) {
				console.error('处理告警失败:', error)
				throw error
			}
		},
	},
	getters: {},
})
