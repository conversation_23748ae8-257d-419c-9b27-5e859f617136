import { ref, computed, watchEffect } from 'vue'
import XFileLink from '../../FileLink/FileLink.vue'
import XImage from '../../Image/Image.vue'

/**
 * 解析复杂的prop配置
 * 1. 点号(.)多层访问：'userinfo.name' -> row.userinfo?.name
 * 2. #文本# 原样输出：'name/#years old#/age' -> "张三/years old/25"
 * 3. 其他情况：按原格式替换属性名为属性值，保留原有符号
 * @param {string} prop - prop配置字符串
 * @param {Object} row - 数据行对象
 * @returns {string|null} - 格式化后的显示内容
 */
const parseComplexProp = (prop: string, row: any): string | null => {
	if (!prop || !row) return null

	// 检查是否是多层访问（只包含字母数字下划线和点号，且不包含#号）
	const isNestedAccess = /^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(prop) && prop.includes('.') && !prop.includes('#')

	if (isNestedAccess) {
		// 处理多层访问：userinfo.name -> row.userinfo?.name
		const value = prop.split('.').reduce((obj: any, key: string) => obj?.[key], row)
		return value !== null && value !== undefined ? String(value) : null
	}

	// 其他情况：先排除#文本#中的内容，然后查找属性名
	let tempProp = prop
	const literalMatches = [...prop.matchAll(/#([^#]+)#/g)]

	// 用占位符替换#文本#部分，避免其中的文本被当作属性名
	literalMatches.forEach((match, index) => {
		tempProp = tempProp.replace(match[0], `~LITERAL${index}~`)
	})

	// 在临时字符串中查找属性名
	const propertyPattern = /[a-zA-Z_][a-zA-Z0-9_]*/g
	const properties = tempProp.match(propertyPattern)?.filter((p) => !p.startsWith('LITERAL')) || []

	if (properties.length === 0 && literalMatches.length === 0) return null

	if (properties.length === 1 && literalMatches.length === 0) {
		// 单个属性直接返回
		const value = row?.[properties[0]]
		return value !== null && value !== undefined ? String(value) : null
	}

	// 在原始prop中替换属性名为属性值
	let result = prop
	properties.forEach((property) => {
		const value = row?.[property]
		const displayValue = value !== null && value !== undefined ? String(value) : ''
		// 使用单词边界匹配，避免部分匹配问题
		result = result.replace(new RegExp(`\\b${property}\\b`, 'g'), displayValue)
	})

	// 清理#符号，保留#号之间的文本
	result = result.replace(/#([^#]+)#/g, '$1')

	return result
}

/**
 * 处理表格列格式化的逻辑
 * 包括标签格式化、文件链接、HTML内容、组件渲染等
 * @param {Object} props - 组件props
 * @param {Array} flattenedColumns - 扁平化的列配置
 * @returns {Object} - 列格式化相关的方法和数据
 */
export function useColumnFormatter(props: any, flattenedColumns: any) {
	// 存储各个标签组的数据
	const tagGroupsData = ref(new Map())

	// 获取所有需要加载标签的列
	const columnsWithTags = computed(() => {
		return flattenedColumns.value.filter((col: any) => col.tagGroupName)
	})

	// 加载标签数据
	const loadTagData = async () => {
		const loadPromises = columnsWithTags.value.map(async (col: any) => {
			if (!tagGroupsData.value.has(col.tagGroupName)) {
				try {
					// 直接调用API获取标签数据
					const tagListData = await BIZ_OPEN_APIS.getTagListStore(`${col.tagGroupName}&format=${col.tagValueFormat || 'default'}`)

					// 将标签列表转换为Map便于查找
					const tagMap = new Map()
					tagListData?.tagList?.forEach((tag: any) => {
						tagMap.set(tag.id, tag.name)
					})

					tagGroupsData.value.set(col.tagGroupName, tagMap)
				} catch (error) {
					console.warn(`Failed to load tag group: ${col.tagGroupName}`, error)
					tagGroupsData.value.set(col.tagGroupName, new Map())
				}
			}
		})

		await Promise.all(loadPromises)
	}

	// 根据列配置和值获取标签label
	const getTagLabel = (col: any, value: any) => {
		if (!col.tagGroupName || value === null || value === undefined) {
			return null
		}

		const tagMap = tagGroupsData.value.get(col.tagGroupName)
		if (!tagMap) {
			return null
		}

		return tagMap.get(value) || null
	}

	// 获取列的样式类名
	const getColumnStyles = (col: any, rawValue: any, tagLabel: any) => {
		if (!col.styles || typeof col.styles !== 'object') {
			return ''
		}

		// 优先通过原始值（prop）匹配样式
		if (col.styles[rawValue] !== undefined) {
			return col.styles[rawValue]
		}

		// 尝试字符串形式的原始值匹配
		const stringValue = String(rawValue)
		if (col.styles[stringValue] !== undefined) {
			return col.styles[stringValue]
		}

		// 通过标签文本（labelvalue）匹配样式
		if (tagLabel && col.styles[tagLabel] !== undefined) {
			return col.styles[tagLabel]
		}

		// 返回默认样式
		return col.styles.default || ''
	}

	// 格式化列值（包含标签处理和样式处理）
	const formatColumnValue = (row: any, col: any) => {
		if (!row || !col) return { type: 'text', content: '', tagLabel: null, rawValue: null, styleClasses: '' }

		// 获取原始值
		let rawValue = null
		let content = ''

		if (col?.formatter) {
			const value = col.formatter(row[col?.[props.colValueKey]], row)
			rawValue = row[col?.[props.colValueKey]]
			content = value
		} else {
			const prop = col?.[props.colValueKey]
			if (prop) {
				// 解析复杂的prop配置
				const result = parseComplexProp(prop, row)
				if (result !== null) {
					rawValue = result
					content = result
				}
			}
		}

		// 获取标签label
		const tagLabel = getTagLabel(col, rawValue)

		// 如果有标签且没有自定义formatter，使用标签label作为显示内容
		if (tagLabel && !col?.formatter) {
			content = tagLabel
		}

		// 获取样式类名
		const styleClasses = getColumnStyles(col, rawValue, tagLabel)

		// 格式化最终值
		const formattedValue = formatCellValue(content, col, row, rawValue)

		return {
			...formattedValue,
			tagLabel,
			rawValue,
			styleClasses,
		}
	}

	// 格式化单元格值
	const formatCellValue = (value: any, col?: any, row?: any, rawValue?: any) => {
		if (typeof value === 'function') {
			return { type: 'render', render: value }
		}

		// 处理图片类型的列
		if (col?.type === 'img' || col?.type === 'image') {
			// 获取图片URL
			let src = ''

			if (col.srcProp) {
				// 从指定的属性获取图片URL
				src = col.srcProp.split('.').reduce((obj: any, key: string) => obj?.[key], row) || ''
			} else if (rawValue && typeof rawValue === 'string') {
				// 直接使用rawValue作为图片URL
				src = rawValue
			} else if (value && typeof value === 'string') {
				// 使用value作为图片URL
				src = value
			}

			// 获取其他XImage组件的props
			const imageProps: any = {
				src: src,
				mode: col.mode || 'aspectFit', // 默认使用aspectFit模式
			}

			// 如果列配置中有其他XImage的props，合并进去
			if (col.imageProps && typeof col.imageProps === 'object') {
				Object.assign(imageProps, col.imageProps)
			}

			// 设置默认样式
			const defaultStyle = {
				width: col.width || '40px',
				height: col.height || '40px',
			}

			// 合并用户自定义样式
			if (col.style && typeof col.style === 'object') {
				Object.assign(defaultStyle, col.style)
			}

			return {
				type: 'component',
				config: {
					component: XImage,
					props: imageProps,
					style: defaultStyle,
				},
			}
		}

		// 处理filelink类型的列
		if (col?.type === 'fileLink') {
			// 获取url和name
			let url = ''
			let name = ''

			if (col.urlProp) {
				// 从指定的属性获取URL
				url = col.urlProp.split('.').reduce((obj: any, key: string) => obj?.[key], row) || ''
			} else if (rawValue && typeof rawValue === 'string') {
				// 直接使用rawValue作为URL
				url = rawValue
			}

			if (col.nameProp) {
				// 从指定的属性获取文件名
				name = col.nameProp.split('.').reduce((obj: any, key: string) => obj?.[key], row) || ''
			} else if (col.name) {
				// 使用列配置中的固定名称
				name = col.name
			}

			return {
				type: 'component',
				config: {
					component: XFileLink,
					props: {
						url: url,
						name: name,
					},
				},
			}
		}

		if (value && typeof value === 'object' && value.component) {
			return { type: 'component', config: value }
		}

		if (typeof value === 'string' && value.trim().startsWith('<')) {
			return { type: 'html', content: value, ellipsis: props.ellipsis }
		}

		return { type: 'text', content: value }
	}

	// 监听列配置变化，重新加载标签数据
	watchEffect(() => {
		if (columnsWithTags.value.length > 0) {
			loadTagData()
		}
	})

	return {
		tagGroupsData,
		formatColumnValue,
		getTagLabel,
		loadTagData,
	}
}
