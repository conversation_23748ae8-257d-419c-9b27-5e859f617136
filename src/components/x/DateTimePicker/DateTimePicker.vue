<script setup>
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
dayjs.extend(isBetween)
import { X_DATE_UTILS } from '../utils/xutils'

const props = defineProps({
	type: { type: String, default: 'datetime' }, // date | datetime
	range: { type: Boolean, default: false },
	immediateClose: {
		type: Boolean,
		default: (props) => !props.range && props.type === 'date',
	},
	clearable: { type: Boolean, default: true },
	displayFormat: { type: String, default: '' },
	valueFormat: { type: String, default: 'YYYY-MM-DD HH:mm:ss' },
	placeholder: { type: String, default: '请选择日期时间' },
	disabled: { type: Boolean, default: false },
	defaultCurrentTime: { type: Boolean, default: false },
	// 是否显示左侧快捷选项（今天/本周/本月）
	showShortcuts: { type: <PERSON>olean, default: false },
})

const popupRef = ref()

// 快捷按钮配置（顺序可自由调整）
const shortcuts = [
	{ key: 'today', label: '今天', fn: X_DATE_UTILS.getTodayRange },
	{ key: 'thisWeek', label: '本周', fn: X_DATE_UTILS.getThisWeekRange },
	{ key: 'thisMonth', label: '本月', fn: X_DATE_UTILS.getThisMonthRange },
	{ key: 'thisQuarter', label: '本季度', fn: X_DATE_UTILS.getThisQuarterRange },
	{ key: 'yesterday', label: '昨日', fn: X_DATE_UTILS.getYesterdayRange },
	{ key: 'last7', label: '近7天', fn: X_DATE_UTILS.getLast7DaysRange },
	{ key: 'last30', label: '近30天', fn: X_DATE_UTILS.getLast30DaysRange },
]

const value = defineModel({ type: [String, Number, Date, Array], default: undefined })
const shortcutLabel = defineModel('shortcutLabel', { type: String, default: '' })
const emit = defineEmits(['update:modelValue', 'update:shortcutLabel', 'confirm', 'clear'])

// 表单上下文处理
const formContext = inject('formContext', null)
const { prop: formItemProp } = inject('formItemContext', {})

// 状态管理 - 合并相关状态
const state = reactive({
	currentDate: dayjs(),
	showYearPanel: false,
	showMonthPanel: false,
	showTimePanel: props.type === 'datetime',
	tempDate: props.range ? [] : null,
	tempTime: ['00', '00', '00'],
	selectedDateIndex: 0,
	tempShortcutLabel: '', // 临时的快捷选项标签，只有确认后才更新到 shortcutLabel
})

// 统一的日期处理工具函数
const dateUtils = {
	// 安全创建dayjs对象
	create(date, format) {
		if (!date) return null
		const dayjsObj = format ? dayjs(date, format) : dayjs(date)
		return dayjsObj.isValid() ? dayjsObj : null
	},

	// 格式化日期值
	formatValue(date, format) {
		if (!date?.isValid()) return null
		if (format === 'x') return date.valueOf()
		if (format) return date.format(format)
		return date.toDate()
	},

	// 解析输入值
	parseValue(value) {
		if (!value) return props.range ? [] : null

		if (props.range) {
			if (!Array.isArray(value)) return []
			return value.map((date) => (props.valueFormat === 'x' ? dayjs(Number(date)) : this.create(date, props.valueFormat))).filter(Boolean)
		}

		return props.valueFormat === 'x' ? dayjs(Number(value)) : this.create(value, props.valueFormat)
	},
}

// 范围选择逻辑优化
const rangeSelection = {
	// 处理范围选择点击
	handleClick(clickedDate) {
		const dateArray = Array.isArray(state.tempDate) ? state.tempDate : []

		if (dateArray.length === 0) {
			// 第一次选择
			state.tempDate = [clickedDate]
			state.selectedDateIndex = 0
		} else if (dateArray.length === 1) {
			// 第二次选择
			const [start] = dateArray
			if (clickedDate.isSame(start, 'day')) {
				state.tempDate = [start, start]
			} else {
				state.tempDate = clickedDate.isBefore(start) ? [clickedDate, start] : [start, clickedDate]
			}
			state.selectedDateIndex = 1
			// 确保结束时间为23:59:59
			if (state.tempDate[1].hour() === 0 && state.tempDate[1].minute() === 0) {
				state.tempDate[1] = state.tempDate[1].endOf('day')
			}
		} else {
			// 重新选择
			const isClickingExisting = dateArray.some((date, index) => {
				if (date?.isValid() && clickedDate.isSame(date, 'day')) {
					state.selectedDateIndex = index
					return true
				}
				return false
			})

			if (!isClickingExisting) {
				state.tempDate = [clickedDate]
				state.selectedDateIndex = 0
			}
		}
	},

	// 检查是否为同一天范围
	isSameDay() {
		const { tempDate } = state
		return (
			props.range &&
			Array.isArray(tempDate) &&
			tempDate.length === 2 &&
			tempDate[0]?.isValid() &&
			tempDate[1]?.isValid() &&
			tempDate[0].isSame(tempDate[1], 'day')
		)
	},
}

// 初始化默认值
onMounted(() => {
	// 先处理 defaultCurrentTime
	if (props.defaultCurrentTime && !value.value) {
		const now = dayjs()
		const formatValue = (date) => dateUtils.formatValue(date, props.valueFormat)

		if (props.range) {
			value.value = [formatValue(now.startOf('day')), formatValue(now.endOf('day'))]
		} else {
			value.value = formatValue(now)
		}
	}

	// 然后初始化值，处理 v-model 和 v-model:shortcutLabel 的冲突
	nextTick(() => {
		initializeValues()
	})
})

// 显示格式
const displayFormat = computed(() => props.displayFormat || (props.type === 'datetime' ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'))

// 更新时间显示
const updateTempTime = () => {
	if (props.type !== 'datetime') return

	const selectedDate = props.range ? state.tempDate?.[state.selectedDateIndex] : state.tempDate

	if (selectedDate?.isValid()) {
		state.tempTime = [selectedDate.format('HH'), selectedDate.format('mm'), selectedDate.format('ss')]
	} else {
		state.tempTime = ['00', '00', '00']
	}
}

// 计算显示值
const computedDisplayValue = computed(() => {
	if (!value.value) return ''

	try {
		if (props.range) {
			if (!Array.isArray(value.value) || value.value.length < 2) return ''

			return value.value
				.map((date) => {
					const dayjsDate = props.valueFormat === 'x' ? dayjs(Number(date)) : dateUtils.create(date)
					return dayjsDate?.format(displayFormat.value) || ''
				})
				.filter(Boolean)
				.join(' ~ ')
		}

		const dayjsDate = props.valueFormat === 'x' ? dayjs(Number(value.value)) : dateUtils.create(value.value)
		return dayjsDate?.format(displayFormat.value) || ''
	} catch {
		return ''
	}
})

// 初始化标志，避免初始化时的冲突
const isInitialized = ref(false)

// 静默应用快捷选项（不更新 shortcutLabel，避免循环）
const applyShortcutSilently = (shortcut) => {
	if (props.disabled || !shortcut?.fn) return
	// 同时设置临时状态
	state.tempShortcutLabel = shortcut.label
	const [startMs, endMs] = shortcut.fn()
	const start = dayjs(startMs)
	const end = dayjs(endMs)
	if (props.range) {
		state.tempDate = [start, end]
		state.selectedDateIndex = 1
	} else {
		state.tempDate = start
	}
	state.currentDate = start
	updateTempTime()
	// 更新 value 但不更新 shortcutLabel
	const result = props.range
		? state.tempDate.map((date) => dateUtils.formatValue(date, props.valueFormat)).filter(Boolean)
		: dateUtils.formatValue(state.tempDate, props.valueFormat)
	value.value = result
}

// 初始化处理函数
const initializeValues = () => {
	if (isInitialized.value) return

	// 检查哪个有值
	const hasValue = value.value != null && value.value !== '' && (!Array.isArray(value.value) || value.value.length > 0)
	const hasShortcutLabel = shortcutLabel.value != null && shortcutLabel.value !== ''

	if (hasShortcutLabel && !hasValue) {
		// 只有 shortcutLabel 有值，应用快捷选项
		const shortcut = shortcuts.find((s) => s.label === shortcutLabel.value)
		if (shortcut) {
			applyShortcutSilently(shortcut)
		}
	} else if (hasValue) {
		// value 有值（无论 shortcutLabel 是否有值，都优先使用 value）
		state.tempDate = dateUtils.parseValue(value.value)
		updateTempTime()
		// 如果同时有 shortcutLabel，设置临时标签但不应用快捷选项
		if (hasShortcutLabel) {
			state.tempShortcutLabel = shortcutLabel.value
		}
	}

	isInitialized.value = true
}

// 监听值变化
watch(
	() => value.value,
	(val) => {
		if (!isInitialized.value) return // 初始化期间不处理
		state.tempDate = dateUtils.parseValue(val)
		updateTempTime()
		// 如果值被清空，也清空快捷选项标签
		if (!val || (Array.isArray(val) && val.length === 0)) {
			state.tempShortcutLabel = ''
		}
	},
	{ deep: true },
)

// 监听 shortcutLabel 变化，如果有值则应用对应的快捷选项
watch(
	() => shortcutLabel.value,
	(newLabel) => {
		if (!isInitialized.value) return // 初始化期间不处理

		if (newLabel) {
			// 查找对应的快捷选项
			const shortcut = shortcuts.find((s) => s.label === newLabel)
			if (shortcut) {
				// 应用快捷选项但不触发 shortcutLabel 更新（避免循环）
				applyShortcutSilently(shortcut)
			}
		} else {
			// 如果 shortcutLabel 被清空，也清空临时状态
			state.tempShortcutLabel = ''
		}
	},
)

// 应用时间到日期
const applyTimeToDate = () => {
	if (!state.tempDate) return

	const [hours, minutes, seconds] = state.tempTime.map((v) => parseInt(v) || 0)

	if (props.range && Array.isArray(state.tempDate)) {
		const targetDate = state.tempDate[state.selectedDateIndex]
		if (targetDate?.isValid()) {
			state.tempDate[state.selectedDateIndex] = targetDate.hour(hours).minute(minutes).second(seconds)
		}
	} else if (state.tempDate?.isValid()) {
		state.tempDate = state.tempDate.hour(hours).minute(minutes).second(seconds)
	}
}

// 处理日期点击
const handleDateClick = (date) => {
	if (props.disabled || !date?.isValid()) return

	const clickedDate = date.startOf('day')

	if (props.range) {
		rangeSelection.handleClick(clickedDate)
	} else {
		state.tempDate = clickedDate
	}

	// 手动选择日期时，如果外部没有设置 shortcutLabel，则清空临时快捷选项标签
	if (!shortcutLabel.value) {
		state.tempShortcutLabel = ''
	}

	updateTempTime()
	if (props.immediateClose) confirm()
}

// 处理时间变化
const handleTimeChange = (type, value) => {
	if (props.disabled || value == null) return

	const timeTypes = ['hour', 'minute', 'second']
	const index = timeTypes.indexOf(type)
	if (index === -1) return

	const numValue = parseInt(value)
	const maxValue = type === 'hour' ? 23 : 59

	if (numValue >= 0 && numValue <= maxValue) {
		state.tempTime[index] = String(numValue).padStart(2, '0')
		applyTimeToDate()
	}
}

// 确认选择
const confirm = async () => {
	if (props.disabled || !state.tempDate) {
		emit('confirm', null)
		closePopup()
		return
	}

	// 验证数据有效性
	if (props.range) {
		if (!Array.isArray(state.tempDate) || state.tempDate.length === 0) {
			closePopup()
			return
		}
	} else if (!state.tempDate?.isValid()) {
		closePopup()
		return
	}

	try {
		const result = props.range
			? state.tempDate.map((date) => dateUtils.formatValue(date, props.valueFormat)).filter(Boolean)
			: dateUtils.formatValue(state.tempDate, props.valueFormat)

		value.value = result
		if (state.tempShortcutLabel) {
			shortcutLabel.value = state.tempShortcutLabel
		}
		emit('confirm', result)

		// 触发表单校验
		formContext?.validateField?.(formItemProp, 'change')
	} catch {
		// 出错时不更新值
	}

	closePopup()
}

// 日历计算
const calendarDays = computed(() => {
	const startOfMonth = state.currentDate.startOf('month')
	const days = []
	const firstDay = startOfMonth.day()
	const daysInMonth = startOfMonth.daysInMonth()

	// 当前月份日期
	for (let i = 1; i <= daysInMonth; i++) {
		days.push(startOfMonth.date(i))
	}

	// 前面补充上个月日期
	const prevMonth = startOfMonth.subtract(1, 'month')
	for (let i = firstDay - 1; i >= 0; i--) {
		days.unshift(prevMonth.date(prevMonth.daysInMonth() - i))
	}

	// 后面补充下个月日期，保证42天
	while (days.length < 42) {
		days.push(days[days.length - 1].add(1, 'day'))
	}

	return days
})

// 工具函数
const closePopup = () => popupRef.value?.close()

const applyShortcut = (shortcut) => {
	if (props.disabled || !shortcut?.fn) return
	const [startMs, endMs] = shortcut.fn()
	const start = dayjs(startMs)
	const end = dayjs(endMs)
	if (props.range) {
		state.tempDate = [start, end]
		state.selectedDateIndex = 1
	} else {
		state.tempDate = start
	}
	state.currentDate = start
	updateTempTime()

	state.tempShortcutLabel = shortcut.label
	if (props.immediateClose) confirm()
}

const selectToday = () => {
	if (props.disabled) return
	const today = dayjs()
	state.currentDate = today
	state.tempDate = props.range ? [today.startOf('day'), today.startOf('day')] : today.startOf('day')
	// 如果外部没有设置 shortcutLabel，则设置临时快捷选项标签为"今天"
	if (!shortcutLabel.value) {
		state.tempShortcutLabel = '今天'
	}
	if (props.immediateClose) confirm()
}

// 处理清除
const handleClear = () => {
	state.tempDate = null
	value.value = null
	state.tempShortcutLabel = ''
	shortcutLabel.value = ''
	emit('confirm', null)
	emit('clear')

	// 触发表单校验
	formContext?.validateField?.(formItemProp, 'change')
}

// 导航功能
const navigate = (unit, direction) => {
	if (props.disabled) return
	state.currentDate = direction === 'next' ? state.currentDate.add(1, unit) : state.currentDate.subtract(1, unit)
}

// 年份和月份
const years = computed(() => {
	const currentYear = state.currentDate.year()
	return Array.from({ length: 12 }, (_, i) => currentYear - 6 + i)
})

const months = Array.from({ length: 12 }, (_, i) => i + 1)

// 年月选择方法
const selectYear = (year) => {
	state.currentDate = state.currentDate.year(year)
	state.showYearPanel = false
}

const selectMonth = (month) => {
	state.currentDate = state.currentDate.month(month - 1)
	state.showMonthPanel = false
}

// 计算属性
const isSameDayRange = computed(() => rangeSelection.isSameDay())

function handlePopupOpen() {
	const shortcut = shortcuts.find((s) => s.label === shortcutLabel.value)
	if (shortcut) {
		// 应用快捷选项但不触发 shortcutLabel 更新（避免循环）
		applyShortcutSilently(shortcut)
	}
}
</script>

<template>
	<x-popup ref="popupRef" :disabled="disabled" :offset="4" @open="handlePopupOpen">
		<XInput
			:class="$attrs.class"
			readonly
			:placeholder="placeholder"
			:model-value="computedDisplayValue"
			:clearable="clearable && !disabled"
			:disabled="disabled"
			@clear="handleClear"
		/>
		<template #content>
			<div class="flex overflow-hidden rounded bg-inputBg color-inputText shadow border-inputBorder">
				<!-- 左侧快捷选项 -->
				<div v-if="showShortcuts && range" class="w-7rem flex flex-col gap-2 p-2 border-r-border">
					<XButton
						v-for="s in shortcuts"
						:key="s.key"
						size="small"
						:class="{
							'!bg-primaryBg !text-white': state.tempShortcutLabel !== s.label,
						}"
						@click="() => applyShortcut(s)"
					>
						{{ s.label }}
					</XButton>
				</div>
				<div class="w-18rem">
					<!-- 头部 -->
					<div class="flex items-center justify-between p-sm-base border-b-border">
						<div class="flex items-center gap-xs">
							<XIconsChevronDoubleLeft v-show="!state.showMonthPanel" class="cursor-pointer" @click.stop="navigate('year', 'prev')" />
							<XIconsChevronLeft v-show="!state.showYearPanel" class="cursor-pointer" @click.stop="navigate('month', 'prev')" />
						</div>
						<div class="flex flex-1 items-center justify-center gap-xxs">
							<div
								v-show="!state.showMonthPanel"
								class="cursor-pointer rounded px-xxs hover:bg-gray-100"
								@click="state.showYearPanel = !state.showYearPanel"
							>
								{{ state.currentDate.format('YYYY') }}年
							</div>
							<div
								v-show="!state.showYearPanel"
								class="cursor-pointer rounded px-xxs hover:bg-gray-100"
								@click="state.showMonthPanel = !state.showMonthPanel"
							>
								{{ state.currentDate.format('MM') }}月
							</div>
						</div>
						<div class="flex rotate-180 items-center gap-xs">
							<XIconsChevronDoubleLeft v-show="!state.showMonthPanel" class="cursor-pointer" @click.stop="navigate('year', 'next')" />
							<XIconsChevronLeft v-show="!state.showYearPanel" class="cursor-pointer" @click.stop="navigate('month', 'next')" />
						</div>
					</div>

					<div class="relative">
						<!-- 年份选择 -->
						<div v-show="state.showYearPanel" class="absolute z-2 grid grid-cols-3 hfull wfull gap-2 border rounded bg-white p-2 shadow">
							<button v-for="year in years" :key="year" class="rounded text-center hover:bg-blue-100" @click.stop="selectYear(year)">
								{{ year }}
							</button>
						</div>

						<!-- 月份选择 -->
						<div v-show="state.showMonthPanel" class="absolute z-2 grid grid-cols-3 hfull wfull gap-2 border rounded bg-white p-2 shadow">
							<button v-for="month in months" :key="month" class="rounded text-center hover:bg-blue-100" @click.stop="selectMonth(month)">
								{{ month }}
							</button>
						</div>

						<!-- 日期面板 -->
						<div class="grid grid-cols-7 gap-1 p-2">
							<div v-for="day in ['日', '一', '二', '三', '四', '五', '六']" :key="day" class="text-center text-gray-500 text-sm">
								{{ day }}
							</div>
							<button
								v-for="(day, index) in calendarDays"
								:key="index"
								class="relative h-2.5rem rounded"
								:class="{
									'opacity-50': !day.isSame(state.currentDate, 'month'),
									'bg-primary text-white': !range && state.tempDate?.isValid() && day.isSame(state.tempDate, 'day'),
									'bg-primary-300':
										range &&
										Array.isArray(state.tempDate) &&
										state.tempDate.length === 2 &&
										state.tempDate[0]?.isValid() &&
										state.tempDate[1]?.isValid() &&
										day.isBetween(state.tempDate[0], state.tempDate[1], 'day', '[]'),
									'hover:bg-blue-100': !range && (!state.tempDate?.isValid() || !day.isSame(state.tempDate, 'day')),
									'border-inputBorder':
										range &&
										Array.isArray(state.tempDate) &&
										state.tempDate[state.selectedDateIndex]?.isValid() &&
										day.isSame(state.tempDate[state.selectedDateIndex], 'day'),
								}"
								@click="handleDateClick(day)"
							>
								<div class="relative h-full flex flex-col items-center justify-center">
									<span>{{ day.date() }}</span>
									<!-- 今日标识 -->
									<span v-if="day.isSame(dayjs(), 'day')" class="absolute top-0 text-8px text-primary font-bold">Today</span>
									<!-- 范围标识 -->
									<span v-if="range && Array.isArray(state.tempDate) && state.tempDate.length > 0" class="absolute bottom-0 text-[10px] leading-3">
										<template v-if="isSameDayRange && day.isSame(state.tempDate[state.selectedDateIndex], 'day')">起止</template>
										<template v-else>
											<template v-if="state.tempDate[0]?.isValid() && day.isSame(state.tempDate[0], 'day')">开始</template>
											<template v-else-if="state.tempDate[1]?.isValid() && day.isSame(state.tempDate[1], 'day')">结束</template>
										</template>
									</span>
								</div>
							</button>
						</div>

						<!-- 时间选择 -->
						<div v-if="state.showTimePanel" class="flex gap-2 p-2">
							<select
								v-for="(type, index) in ['hour', 'minute', 'second']"
								:key="type"
								class="time-select cursor-pointer appearance-none border rounded bg-inputBg px-3 py-2 color-inputText outline-none transition-colors duration-200 border-inputBorder focus:ring-1 focus:ring-primary/30 focus:border-primary hover:border-primaryLight"
								:value="state.tempTime[index]"
								@change="handleTimeChange(type, $event.target.value)"
							>
								<option
									v-for="n in type === 'hour' ? 24 : 60"
									:key="n"
									:value="String(n - 1).padStart(2, '0')"
									class="time-option bg-inputBg py-1 color-inputText !outline-none !border-none"
								>
									{{ String(n - 1).padStart(2, '0') }}
								</option>
							</select>
						</div>

						<!-- 底部 -->
						<div class="flex justify-between p-2 border-t">
							<div>
								<XButton class="" @click="selectToday">今天</XButton>
							</div>
							<div>
								<XButton v-if="!immediateClose" @click="confirm">确定</XButton>
							</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</x-popup>
</template>

<style scoped>
/* 时间选择器样式 */
.time-select {
	/* 移除默认的下拉箭头 */
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
	background-position: right 0.5rem center;
	background-repeat: no-repeat;
	background-size: 1.5em 1.5em;
	padding-right: 2.5rem;
	min-width: 4rem;
}
</style>
