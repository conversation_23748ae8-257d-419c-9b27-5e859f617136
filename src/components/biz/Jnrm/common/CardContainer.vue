<template>
	<div class="glass flex-1 rounded-lg p-3">
		<h2 class="mb-3 flex items-center font-bold text-lg">
			<div class="mr-sm" :class="icon"></div>
			<component :is="iconComponent" class="mr-2 h-5 w-5" />
			{{ title }}
		</h2>
		<div class="card-content">
			<slot></slot>
		</div>
	</div>
</template>

<script setup lang="ts">
// defineProps is a compiler macro and no longer needs to be imported

interface Props {
	title: string
	iconComponent?: any
	icon?: ''
}

defineProps<Props>()
</script>

<style scoped>
.glass {
	background: rgba(255, 255, 255, 0.05);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
	box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}
</style>
