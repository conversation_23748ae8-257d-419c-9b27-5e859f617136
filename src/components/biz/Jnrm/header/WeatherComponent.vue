<template>
	<div class="flex cursor-pointer items-center rounded-lg bg-gray-800 bg-opacity-40 px-3 py-1.5" :title="weatherTooltip" @click="refreshWeather">
		<!-- 天气图标 -->
		<div class="relative mr-1.5">
			<svg v-if="!loading" xmlns="http://www.w3.org/2000/svg" :class="weatherIcon.class" fill="none" viewBox="0 0 24 24" stroke="currentColor">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					:d="weatherIcon.path"
				/>
			</svg>
			<!-- 加载动画 -->
			<div v-else class="h-6 w-6 animate-spin border-yellow-300 rounded-full border-t-transparent border-2"></div>
		</div>
		<div>
			<div class="flex items-baseline">
				<span class="font-bold text-base">{{ weatherData.temperature }}°C</span>
				<span class="ml-1 text-gray-300 text-xs">{{ weatherData.weather }}</span>
			</div>
			<!--      <div v-if="weatherData.city" class="text-xs text-gray-400">{{ weatherData.city }}</div>-->
		</div>
	</div>
</template>

<script setup lang="ts">
import type { WeatherData } from '@/api/biz/other/weatherapi'

// 天气组件 - 显示当前天气信息
interface Props {
	/** 位置信息，如果不提供则自动获取 */
	location?: string
	/** 自动刷新间隔（分钟），0表示不自动刷新 */
	autoRefreshInterval?: number
}

const props = withDefaults(defineProps<Props>(), {
	location: '',
	autoRefreshInterval: 30, // 默认30分钟刷新一次
})

// 响应式数据
const weatherData = ref<WeatherData>({
	temperature: 24,
	weather: '晴',
	city: '山西太原',
})

const loading = ref(false)
const lastUpdateTime = ref<Date | null>(null)

// 计算属性
const weatherTooltip = computed(() => {
	const data = weatherData.value
	const updateTime = lastUpdateTime.value
	let tooltip = `${data.city} ${data.temperature}°C ${data.weather}`

	if (data.humidity) tooltip += `\n湿度: ${data.humidity}`
	if (data.windDirection && data.windSpeed) tooltip += `\n风向: ${data.windDirection} ${data.windSpeed}`
	if (updateTime) tooltip += `\n更新时间: ${updateTime.toLocaleTimeString()}`
	tooltip += '\n点击刷新'

	return tooltip
})

// 天气图标映射
const weatherIcon = computed(() => {
	const weather = weatherData.value.weather

	// 根据天气类型返回不同的图标
	if (weather.includes('晴')) {
		return {
			class: 'h-6 w-6 text-yellow-300',
			path: 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z'
		}
	} else if (weather.includes('云') || weather.includes('多云')) {
		return {
			class: 'h-6 w-6 text-gray-300',
			path: 'M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z'
		}
	} else if (weather.includes('雨')) {
		return {
			class: 'h-6 w-6 text-blue-300',
			path: 'M8 13l2.165 2.165a1 1 0 001.521-.126L16 9m-1.5-1.5l2.828 2.828a4 4 0 01.106 5.549L14 19.5a3 3 0 01-4.242 0L7.5 17.25a4 4 0 01.106-5.549L10.5 8.5z'
		}
	} else if (weather.includes('雪')) {
		return {
			class: 'h-6 w-6 text-white',
			path: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'
		}
	} else {
		// 默认图标（晴天）
		return {
			class: 'h-6 w-6 text-yellow-300',
			path: 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z'
		}
	}
})

// 获取天气数据
const fetchWeatherData = async () => {
	if (loading.value) return

	loading.value = true
	try {
		let result: WeatherData

		// 直接获取格式化的天气数据
		result = await BIZ_WEATHER_APIS.getFormattedWeather()

		weatherData.value = result
		lastUpdateTime.value = new Date()
	} catch (error) {
		console.error('获取天气数据失败:', error)
		// 保持默认数据
	} finally {
		loading.value = false
	}
}

// 手动刷新天气
const refreshWeather = () => {
	fetchWeatherData()
}

// 自动刷新定时器
let autoRefreshTimer: NodeJS.Timeout | null = null

const setupAutoRefresh = () => {
	if (autoRefreshTimer) {
		clearInterval(autoRefreshTimer)
		autoRefreshTimer = null
	}

	if (props.autoRefreshInterval > 0) {
		autoRefreshTimer = setInterval(
			() => {
				fetchWeatherData()
			},
			props.autoRefreshInterval * 60 * 1000,
		) // 转换为毫秒
	}
}

// 组件挂载时获取天气数据
onMounted(() => {
	fetchWeatherData()
	setupAutoRefresh()
})

// 监听props变化
watch(() => props.autoRefreshInterval, setupAutoRefresh)
watch(() => props.location, fetchWeatherData)

// 组件卸载时清理定时器
onUnmounted(() => {
	if (autoRefreshTimer) {
		clearInterval(autoRefreshTimer)
	}
})
</script>

<style scoped>
/* 天气组件样式 */
</style>
